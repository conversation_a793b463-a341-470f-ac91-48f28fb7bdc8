/**
 * 主应用页面 - 重构版本
 * 🎯 核心价值：数据驱动的矩阵应用，最小化架构，零冗余
 * 📦 功能范围：矩阵渲染、控制面板、模式切换
 * 🔄 架构设计：基于新的核心架构，完全数据驱动的视图
 */

'use client';

import Controls from '@/components/Controls';
import Matrix from '@/components/Matrix';
import Button from '@/components/ui/Button';
import { CloseIcon, MenuIcon } from '@/components/ui/Icons';
import { toast } from '@/components/ui/ToastStore';
import { getCachedCompleteData, getMatrixDataByCoordinate } from '@/core/data/GroupAData';
import { useMatrixStore } from '@/core/matrix/MatrixStore';
import type { Coordinate } from '@/core/matrix/MatrixTypes';
import { useWordInputStore } from '@/core/wordLibrary/WordLibraryStore';
import { useClickOutside } from '@/hooks/useClickOutside';
import { useResponsiveControls } from '@/hooks/useResponsiveControls';
import { useCallback, useEffect, useRef, useState } from 'react';

// ===== 主应用组件 =====

export default function HomePage() {
  // 响应式控制面板逻辑
  const {
    controlsVisible,
    setControlsVisible,
    toggleControls,
    displayMode
  } = useResponsiveControls();

  const [isClient, setIsClient] = useState(false);

  // 填词功能相关状态
  const { bindWordToCell, unbindWordFromCell, config, getCellWord } = useMatrixStore();
  const {
    isActive: isWordInputActive,
    selectedCell,
    matchedLibrary,
    selectedWordIndex,
    availableWords,
    temporaryWord,
    isWordBound,
    activateWordInput,
    deactivateWordInput,
    selectNextWord,
    selectPreviousWord,
    confirmWordSelection,
    clearCellWord
  } = useWordInputStore();



  // 获取当前模式配置
  const mainMode = config.mainMode || 'default';
  const contentMode = config.contentMode || 'blank';

  // 检查是否为【颜色】【词语】模式
  const isColorWordMode = mainMode === 'color' && contentMode === 'word';

  // 使用点击外部检测 Hook
  const floatingControlsRef = useClickOutside(() => {
    if (displayMode === 'floating' && controlsVisible) {
      setControlsVisible(false);
    }
  }, {
    enabled: displayMode === 'floating' && controlsVisible,
    excludeSelectors: ['[title="显示控制面板"]']
  });

  // 点击空白退出填词模式 - 使用全局事件监听
  useEffect(() => {
    if (!isWordInputActive) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // 检查点击是否在填词相关元素内部
      const isClickOnWordInputCell = target.closest('.matrix-cell.word-input-active');
      const isClickOnWordLibrary = target.closest('.word-library-active') || target.closest('[data-word-library]');
      const isClickOnToast = target.closest('[data-toast]');

      // 如果点击不在相关元素内部，退出填词模式
      if (!isClickOnWordInputCell && !isClickOnWordLibrary && !isClickOnToast) {
        // 如果没有绑定词语，清除临时显示
        if (!isWordBound && selectedCell) {
          console.log('清除临时显示词语');
        }
        deactivateWordInput();
        console.log('点击空白退出填词模式');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isWordInputActive, isWordBound, selectedCell, deactivateWordInput]);

  // 确保客户端渲染一致性，避免 hydration 错误
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 键盘事件处理 - 填词模式下的快捷键
  useEffect(() => {
    if (!isWordInputActive) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          selectPreviousWord();
          break;
        case 'ArrowRight':
          event.preventDefault();
          selectNextWord();
          break;
        case 'Enter':
          event.preventDefault();
          const selectedWord = confirmWordSelection();
          if (selectedWord && selectedCell) {
            bindWordToCell(selectedCell.x, selectedCell.y, selectedWord.id);
            console.log('确认选择词语:', selectedWord);
          }
          deactivateWordInput();
          break;
        case 'Escape':
          event.preventDefault();
          // 如果没有绑定词语，清除临时显示
          if (!isWordBound && selectedCell) {
            console.log('ESC键清除临时显示词语');
          }
          deactivateWordInput();
          break;
        case 'Delete':
        case 'Backspace':
          event.preventDefault();
          if (selectedCell) {
            unbindWordFromCell(selectedCell.x, selectedCell.y);
            console.log('删除单元格词语');
          }
          deactivateWordInput();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isWordInputActive, selectPreviousWord, selectNextWord, confirmWordSelection, selectedCell, bindWordToCell, deactivateWordInput, unbindWordFromCell]);

  // 简化的事件处理逻辑已通过 useClickOutside Hook 处理

  // 统一的事件处理 - 简化版本
  const handleEvent = useCallback((type: string, data: any) => {
    console.log(`${type}:`, data);
  }, []);

  // 防抖引用，防止短时间内多次双击
  const doubleClickTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理双击激活填词功能 - 仅在【颜色】【词语】模式下生效
  const handleCellDoubleClick = useCallback(async (coordinate: Coordinate) => {
    // 检查是否为【颜色】【词语】模式
    if (!isColorWordMode) {
      console.log('双击填词功能仅在【颜色】【词语】模式下可用');
      return;
    }

    // 防抖处理：清除之前的定时器
    if (doubleClickTimeoutRef.current) {
      clearTimeout(doubleClickTimeoutRef.current);
    }

    // 设置新的定时器，防止短时间内多次触发
    doubleClickTimeoutRef.current = setTimeout(async () => {
      console.log('双击单元格:', coordinate);

      // 获取矩阵数据以确定颜色和级别
      const matrixData = getCachedCompleteData();
      const cellData = getMatrixDataByCoordinate(matrixData, coordinate.x, coordinate.y);

      if (cellData && cellData.color && cellData.level) {
        // 检查对应词库是否为空
        const { useWordLibraryStore } = require('@/core/wordLibrary/WordLibraryStore');
        const { createWordLibraryKey } = require('@/core/matrix/MatrixTypes');
        const wordLibraryStore = useWordLibraryStore.getState();
        const libraryKey = createWordLibraryKey(cellData.color, cellData.level);
        const library = wordLibraryStore.getLibrary(libraryKey);

        // 如果词库为空，显示中心提示
        if (!library || library.words.length === 0) {
          toast.warning('请先填入词语', {
            position: 'center',
            duration: 2000
          });
          console.log('词库为空，显示中心提醒但仍激活填词模式');
        }

        // 获取当前单元格已绑定的词语
        const boundWordId = getCellWord(coordinate.x, coordinate.y);

        // 激活填词模式，传递绑定信息
        await activateWordInput(coordinate.x, coordinate.y, cellData.color, cellData.level, boundWordId || undefined);

        console.log('激活填词模式:', { coordinate, color: cellData.color, level: cellData.level });
      } else {
        console.log('单元格没有颜色和级别数据，无法激活填词模式');
      }
    }, 100); // 100ms防抖延迟
  }, [activateWordInput, isColorWordMode, getCellWord]);



  // 处理控制面板隐藏按钮点击
  const handleHideControls = useCallback((event: React.MouseEvent) => {
    console.log('🔘 关闭按钮被点击', {
      windowWidth: window.innerWidth,
      displayMode,
      controlsVisible,
      eventType: event.type,
      target: event.target
    });

    event.preventDefault(); // 防止默认行为
    event.stopPropagation(); // 防止事件冒泡

    // 使用统一的状态管理，这会自动处理用户操作锁定
    setControlsVisible(false);
  }, [setControlsVisible, displayMode, controlsVisible]);

  // ===== 控制面板内容组件 =====

  const ControlsPanel: React.FC<{
    mode: 'normal' | 'floating';
    onHide: (event: React.MouseEvent) => void;
  }> = ({ mode, onHide }) => {
    // 获取矩阵存储以访问重置功能
    const { setModeConfig, initializeMatrix } = useMatrixStore();

    // 处理标题点击重置
    const handleTitleClick = useCallback(() => {
      // 重置到默认模式下的空白模式
      setModeConfig('default', 'blank');
      initializeMatrix();
      handleEvent('Matrix reset', null);
    }, [setModeConfig, initializeMatrix]);

    return (
      <div className="h-full flex flex-col">
        {/* 固定的标题栏 */}
        <div className={`${mode === 'floating' ? 'p-3' : 'p-4'} border-b border-gray-200 relative z-20 flex-shrink-0`}>
          <div className="flex items-center justify-between">
            <h2
              className={`${mode === 'floating' ? 'text-base' : 'text-lg'} font-semibold text-gray-800 cursor-pointer hover:text-red-500 transition-colors duration-200`}
              onClick={handleTitleClick}
              title="点击重置矩阵"
            >
              矩阵系统
            </h2>
            <Button
              variant="icon"
              size="cell"
              onClick={onHide}
              title="隐藏控制面板"
              className={`relative ${mode === 'floating' ? 'z-40' : 'z-30'}`}
            >
              <CloseIcon size={14} />
            </Button>
          </div>
        </div>

        {/* 可滚动的控制内容区域 */}
        <div className="flex-1 overflow-hidden">
          <Controls
            onModeChange={(mode) => handleEvent('Mode changed to', mode)}
          />
        </div>
      </div>
    );
  };




  // 在客户端渲染完成前显示加载状态，避免 hydration 错误
  if (!isClient) {
    return (
      <div className="app-container h-screen flex bg-gray-100 items-center justify-center">
        <div className="text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="app-container min-h-screen flex">
      {/* 主矩阵区域 - 响应式显示，保持1:1比例 */}
      <div className="matrix-area flex-1 flex items-center justify-center p-3" >
        {/* 矩阵容器 - 响应式尺寸，保持1:1比例， */}
        <div className="matrix-container w-full h-full max-w-full max-h-full flex items-center justify-center">
          <Matrix
            onCellClick={(coord) => handleEvent('Cell clicked', coord)}
            onCellDoubleClick={handleCellDoubleClick}
            onModeChange={(mode) => handleEvent('Mode changed to', mode)}
            className=""
            style={{
              width: '100%',
              height: '100%',
              aspectRatio: '1 / 1'
            }}
          />


        </div>
      </div>

      {/* 控制面板 - 响应式显示 */}
      {controlsVisible && displayMode === 'normal' && (
        <div className="controls-sidebar flex w-80 bg-white border-l border-gray-200 flex-shrink-0 flex-col h-screen max-h-screen overflow-hidden">
          <ControlsPanel mode="normal" onHide={handleHideControls} />
        </div>
      )}

      {/* 悬浮控制面板 */}
      {controlsVisible && displayMode === 'floating' && (
        <div
          ref={floatingControlsRef}
          className="fixed top-4 right-4 z-20 w-72 bg-white border border-gray-200 rounded-lg shadow-lg"
        >
          <ControlsPanel mode="floating" onHide={handleHideControls} />
        </div>
      )}



      {/* 菜单图标按钮（当面板隐藏时） */}
      {displayMode === 'hidden' && (
        <div className="fixed top-4 right-4 z-10">
          <Button
            variant="icon"
            size="cell"
            onClick={toggleControls}
            title="显示控制面板"
          >
            <MenuIcon size={18} />
          </Button>
        </div>
      )}



      {/* 应用专用样式 */}
      <style jsx global>{`
        .app-container:focus {
          outline: none;
        }

        /* 极简化设计 - 去除不必要的视觉分割 */
        .matrix-area {
          background: transparent;
        }

        /* 确保矩阵响应式显示，保持1:1长宽比 */
        .matrix-container {
          display: flex;
          align-items: center;
          justify-content: center;
          aspect-ratio: 1 / 1;
          max-width: min(100vw - 2rem, 100vh - 2rem);
          max-height: min(100vw - 2rem, 100vh - 2rem);
        }

        /* 确保矩阵在大屏幕下完整显示 */
        @media (min-width: 1200px) and (min-height: 1200px) {
          .matrix-area {
            padding: 12px;
          }
        }
      `}</style>
    </div>
  );
}